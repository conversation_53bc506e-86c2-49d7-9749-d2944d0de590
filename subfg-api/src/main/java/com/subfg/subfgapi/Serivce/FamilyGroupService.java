package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;

import com.subfg.domain.request.CreateFamilyGroupReq;

@Service
public class FamilyGroupService {

    /**
     * 创建家庭组（统一入口）
     */
    public void createFamilyGroup(CreateFamilyGroupReq req){
        // 根据家庭组类型调用对应的创建方法
        switch (req.getFamilyGroupType()) {
            case SELF_BUILT:
                createSelfBuiltFamilyGroup(req);
                break;
            case GROUP_BUYING:
                createGroupBuyingFamilyGroup(req);
                break;
            default:
                throw new IllegalArgumentException("不支持的家庭组类型: " + req.getFamilyGroupType());
        }
    }

    /**
     * 创建自建家庭组
     */
    private void createSelfBuiltFamilyGroup(CreateFamilyGroupReq req){
        // 自建家庭组创建逻辑
    }

    /**
     * 创建拼团家庭组
     */
    private void createGroupBuyingFamilyGroup(CreateFamilyGroupReq req){
        // 拼团家庭组创建逻辑
    }
}
