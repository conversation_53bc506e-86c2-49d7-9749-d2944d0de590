package com.subfg.subfgapi.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.request.CreateFamilyGroupReq;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.FamilyGroupService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/familyGroup")
@Tag(name = "家庭群组管理", description = "家庭群组管理相关接口")
public class FamilyGroupController {

    private final FamilyGroupService familyGroupService;
    @Operation(summary = "创建自建家庭群组", description = "创建家庭群组")
    @PostMapping("/createSelfBuiltFamilyGroup")
    public Result<String> createSelfBuiltFamilyGroup(@RequestBody CreateFamilyGroupReq req){
        familyGroupService.createSelfBuiltFamilyGroup(req);
        return Result.success();
    }

    /**
     * 加入家庭组
     */
    @PostMapping("/join")
    @Operation(summary = "加入家庭组", description = "加入指定的家庭组")
    public Result<String> joinFamilyGroup(
            @Parameter(description = "家庭组ID") @RequestParam String familyGroupId) {

        // 模拟加入逻辑
        if ("FG_FULL".equals(familyGroupId)) {
            return Result.errorI18n("family.group.full");
        }

        if ("FG_NOT_FOUND".equals(familyGroupId)) {
            return Result.errorI18n("family.group.not.found");
        }

        return Result.successI18n("family.group.join.success");
    }

    /**
     * 离开家庭组
     */
    @PostMapping("/leave")
    @Operation(summary = "离开家庭组", description = "离开当前的家庭组")
    public Result<String> leaveFamilyGroup(
            @Parameter(description = "家庭组ID") @RequestParam String familyGroupId) {

        // 模拟离开逻辑
        if ("FG_NOT_JOINED".equals(familyGroupId)) {
            return Result.errorI18n("family.group.not.joined");
        }

        return Result.successI18n("family.group.leave.success");
    }

    /**
     * 获取家庭组信息
     */
    @GetMapping("/{familyGroupId}")
    @Operation(summary = "获取家庭组信息", description = "获取指定家庭组的详细信息")
    public Result<Map<String, Object>> getFamilyGroupInfo(
            @Parameter(description = "家庭组ID") @PathVariable String familyGroupId) {

        // 模拟查询逻辑
        if ("FG_NOT_FOUND".equals(familyGroupId)) {
            return Result.errorI18n("family.group.not.found");
        }

        Map<String, Object> data = new HashMap<>();
        data.put("familyGroupId", familyGroupId);
        data.put("name", "测试家庭组");
        data.put("memberCount", 3);
        data.put("maxMembers", 6);
        data.put("status", "active");

        return Result.successI18n("common.query.success", data);
    }

}
