package com.subfg.domain.request;

import com.subfg.domain.enums.FamilyGroupType;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;



@Data
@Schema(description = "创建家庭组请求")
public class CreateFamilyGroupReq {

    /**
     * 家庭组类型 1-自建，2-拼团
     */
    @Schema(description = "家庭组类型", example = "SELF_BUILT")
    @NotNull(message = "家庭组类型不能为空")
    private FamilyGroupType familyGroupType;


}