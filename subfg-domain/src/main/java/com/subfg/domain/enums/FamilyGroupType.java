package com.subfg.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;

/**
 * 家庭组类型枚举
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
public enum FamilyGroupType {

    /**
     * 自建家庭组
     */
    SELF_BUILT(1, "自建", "用户自己创建的家庭组"),

    /**
     * 拼团家庭组
     */
    GROUP_BUYING(2, "拼团", "通过拼团方式组建的家庭组");

    /**
     * 类型代码（数据库存储值）
     */
    @JsonValue
    private final Integer code;

    /**
     * 类型名称（显示名称）
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    FamilyGroupType(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举（用于JSON反序列化）
     *
     * @param code 类型代码
     * @return 对应的枚举，如果不存在则返回null
     */
    @JsonCreator
    public static FamilyGroupType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (FamilyGroupType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有类型代码
     *
     * @return 类型代码数组
     */
    public static Integer[] getAllCodes() {
        FamilyGroupType[] types = values();
        Integer[] codes = new Integer[types.length];
        for (int i = 0; i < types.length; i++) {
            codes[i] = types[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有类型名称
     *
     * @return 类型名称数组
     */
    public static String[] getAllNames() {
        FamilyGroupType[] types = values();
        String[] names = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            names[i] = types[i].getName();
        }
        return names;
    }

    /**
     * 获取所有类型描述
     *
     * @return 类型描述数组
     */
    public static String[] getAllDescriptions() {
        FamilyGroupType[] types = values();
        String[] descriptions = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            descriptions[i] = types[i].getDescription();
        }
        return descriptions;
    }
}
