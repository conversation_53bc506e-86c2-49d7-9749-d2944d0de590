package com.subfg.domain.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 家庭组类型验证注解
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = FamilyGroupTypeValidator.class)
@Documented
public @interface ValidFamilyGroupType {

    String message() default "无效的家庭组类型";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
