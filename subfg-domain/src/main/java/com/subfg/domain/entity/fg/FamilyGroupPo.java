package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组实体类
 * 支持自建和拼团两种类型的家庭组
 * 对应数据库表：fg_substore
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_substore")
public class FamilyGroupPo implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 基本信息 ====================

    /**
     * 家庭组ID（主键）
     */
    @TableId(value = "family_group_id", type = IdType.INPUT)
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @TableField("family_group_name")
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    @TableField("description")
    private String description;

    /**
     * 家庭组类型（1-自建，2-拼团）
     */
    @TableField("family_group_type")
    private Integer familyGroupType;

    /**
     * 家庭组状态（-2-不能审核，-1-未通过，0-审核中，1-审核通过组建中，2-已满员，3-暂时不可加入，4-过期，5-关闭）
     */
    @TableField("family_group_status")
    private Integer familyGroupStatus;

    // ==================== 产品订阅信息 ====================

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 产品分类ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 订阅金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 计费周期（月）
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    // ==================== 成员管理信息 ====================

    /**
     * 当前成员人数
     */
    @TableField("join_count")
    private Integer currentMemberCount;

    /**
     * 实际加入人数（已激活的成员）
     */
    @TableField("actual_join_count")
    private Integer activeMemberCount;

    /**
     * 最大成员数（总空位数）
     */
    @TableField("sum_vacancy")
    private Integer maxMemberCount;

    // ==================== 用户信息 ====================

    /**
     * 创建用户ID（家庭组组长）
     */
    @TableField("create_user_id")
    private String createUserId;


    /**
     * 团长ID
     */
    @TableField("group_leader_id")
    private String groupLeaderId;

    // ==================== 时间信息 ====================

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 最新成员加入时间
     */
    @TableField("latest_join_time")
    private Long latestJoinTime;

    /**
     * 服务过期时间
     */
    @TableField("service_expire_time")
    private Long serviceExpireTime;

    // ==================== 审核信息 ====================

    /**
     * 审核图片
     */
    @TableField("review_picture")
    private String reviewPicture;

    /**
     * 审核创建时间
     */
    @TableField("check_create_time")
    private Long checkCreateTime;

    /**
     * 审核时间
     */
    @TableField("check_time")
    private Long checkTime;

    /**
     * 审核用户ID
     */
    @TableField("check_user")
    private String checkUserId;

    /**
     * 审核次数
     */
    @TableField("check_count")
    private Integer checkCount;

    /**
     * 审核失败原因
     */
    @TableField("reason")
    private String reviewFailReason;

    // ==================== 拼团专用字段 ====================

    /**
     * 拼团状态（拼团类型专用：0-等待审核，1-招募中，2-已成团，3-已发车，4-拼团失败，5-已转为自建，-1-审核不通过，-2-已取消）
     */
    @TableField("group_buying_status")
    private Integer groupBuyingStatus;

    /**
     * 拼团截止时间（拼团类型专用）
     */
    @TableField("group_buying_deadline")
    private Long groupBuyingDeadline;

    /**
     * 最少成团人数（拼团类型专用）
     */
    @TableField("min_group_size")
    private Integer minGroupSize;

    /**
     * 最大成团人数（拼团类型专用）
     */
    @TableField("max_group_size")
    private Integer maxGroupSize;

    /**
     * 发车时间（拼团类型专用）
     */
    @TableField("launch_time")
    private Long launchTime;

    /**
     * 拼团规则描述（拼团类型专用）
     */
    @TableField("group_buying_rules")
    private String groupBuyingRules;

    /**
     * 拼团优惠金额（拼团类型专用）
     */
    @TableField("group_discount_amount")
    private BigDecimal groupDiscountAmount;

    /**
     * 转为自建时间（记录拼团转自建的时间）
     */
    @TableField("convert_to_self_built_time")
    private Long convertToSelfBuiltTime;

    /**
     * 原始类型（记录转换前的类型：1-原始自建，2-拼团转自建）
     */
    @TableField("original_type")
    private Integer originalType;

    /**
     * 拼团审核时间（拼团类型专用）
     */
    @TableField("group_review_time")
    private Long groupReviewTime;

    /**
     * 拼团审核用户（拼团类型专用）
     */
    @TableField("group_review_user")
    private String groupReviewUser;

    /**
     * 拼团审核备注（拼团类型专用）
     */
    @TableField("group_review_remark")
    private String groupReviewRemark;

    /**
     * 是否自动发车（拼团类型专用：0-手动发车，1-自动发车）
     */
    @TableField("auto_launch_enabled")
    private Integer autoLaunchEnabled;

}
